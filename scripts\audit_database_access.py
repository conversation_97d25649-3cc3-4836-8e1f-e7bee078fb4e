#!/usr/bin/env python3
"""
Database Access Pattern Audit Script

This script scans the codebase to identify all database access patterns
and provides recommendations for standardization.
"""

import os
import re
import ast
from pathlib import Path
from typing import List, Dict, Tuple
from dataclasses import dataclass

@dataclass
class DatabaseAccessPattern:
    """Represents a database access pattern found in the code"""
    file_path: str
    line_number: int
    pattern_type: str
    code_snippet: str
    recommendation: str

class DatabaseAccessAuditor:
    """Audits database access patterns in the codebase"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.patterns = []
        
        # Patterns to look for
        self.direct_connect_pattern = re.compile(r'sqlite3\.connect\s*\(')
        self.db_path_pattern = re.compile(r'["\'].*\.db["\']')
        self.cursor_pattern = re.compile(r'\.cursor\s*\(\s*\)')
        self.execute_pattern = re.compile(r'\.execute\s*\(')
        
    def audit_file(self, file_path: Path) -> List[DatabaseAccessPattern]:
        """Audit a single Python file for database access patterns"""
        patterns = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # Check for direct sqlite3.connect calls
                if self.direct_connect_pattern.search(line):
                    patterns.append(DatabaseAccessPattern(
                        file_path=str(file_path.relative_to(self.project_root)),
                        line_number=line_num,
                        pattern_type="direct_connect",
                        code_snippet=line_stripped,
                        recommendation="Replace with db_connection() context manager"
                    ))
                
                # Check for hardcoded database paths
                if self.db_path_pattern.search(line) and '.db' in line:
                    patterns.append(DatabaseAccessPattern(
                        file_path=str(file_path.relative_to(self.project_root)),
                        line_number=line_num,
                        pattern_type="hardcoded_path",
                        code_snippet=line_stripped,
                        recommendation="Use ConfigManager.get_database_config()"
                    ))
                
                # Check for cursor usage without context manager
                if self.cursor_pattern.search(line) and 'with' not in line:
                    patterns.append(DatabaseAccessPattern(
                        file_path=str(file_path.relative_to(self.project_root)),
                        line_number=line_num,
                        pattern_type="unsafe_cursor",
                        code_snippet=line_stripped,
                        recommendation="Use db_connection() or db_transaction() context manager"
                    ))
                
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            
        return patterns
    
    def audit_project(self) -> List[DatabaseAccessPattern]:
        """Audit the entire project for database access patterns"""
        all_patterns = []
        
        # Find all Python files
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            # Skip virtual environment and cache directories
            if any(part in str(file_path) for part in ['.venv', '__pycache__', '.git']):
                continue
                
            patterns = self.audit_file(file_path)
            all_patterns.extend(patterns)
        
        return all_patterns
    
    def generate_report(self, patterns: List[DatabaseAccessPattern]) -> str:
        """Generate a detailed audit report"""
        report = []
        report.append("# Database Access Pattern Audit Report")
        report.append(f"Total issues found: {len(patterns)}")
        report.append("")
        
        # Group by pattern type
        by_type = {}
        for pattern in patterns:
            if pattern.pattern_type not in by_type:
                by_type[pattern.pattern_type] = []
            by_type[pattern.pattern_type].append(pattern)
        
        for pattern_type, type_patterns in by_type.items():
            report.append(f"## {pattern_type.replace('_', ' ').title()} ({len(type_patterns)} issues)")
            report.append("")
            
            for pattern in type_patterns:
                report.append(f"**File:** {pattern.file_path}:{pattern.line_number}")
                report.append(f"**Code:** `{pattern.code_snippet}`")
                report.append(f"**Recommendation:** {pattern.recommendation}")
                report.append("")
        
        # Priority recommendations
        report.append("## Priority Recommendations")
        report.append("")
        report.append("1. **High Priority**: Replace all direct sqlite3.connect() calls")
        report.append("2. **Medium Priority**: Centralize database path configuration")
        report.append("3. **Low Priority**: Ensure proper cursor cleanup")
        report.append("")
        
        return "\n".join(report)

def main():
    """Run the database access audit"""
    project_root = os.getcwd()
    auditor = DatabaseAccessAuditor(project_root)
    
    print("Scanning project for database access patterns...")
    patterns = auditor.audit_project()
    
    print(f"Found {len(patterns)} database access issues")
    
    # Generate report
    report = auditor.generate_report(patterns)
    
    # Save report
    report_path = Path(project_root) / "database_access_audit.md"
    with open(report_path, 'w') as f:
        f.write(report)
    
    print(f"Audit report saved to: {report_path}")
    
    # Print summary
    print("\nSummary:")
    by_type = {}
    for pattern in patterns:
        by_type[pattern.pattern_type] = by_type.get(pattern.pattern_type, 0) + 1
    
    for pattern_type, count in by_type.items():
        print(f"  {pattern_type}: {count}")

if __name__ == "__main__":
    main()
