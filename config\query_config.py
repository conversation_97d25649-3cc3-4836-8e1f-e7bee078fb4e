"""
Query Configuration Management System

This module provides centralized configuration management for query processing,
including document retrieval, response formatting, and anti-hallucination settings.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class QueryConfiguration:
    """Centralized configuration for query processing"""
    
    # Document Retrieval Settings
    retrieval_k: int = 12
    relevance_threshold: float = 0.15
    min_documents: int = 3
    max_documents: int = 8
    
    # Response Display Limits
    max_pdf_images_display: int = 5
    max_url_images_display: int = 5
    max_tables_display: int = 3
    max_pdf_links_display: int = 10
    
    # Context Processing
    max_vision_context_length: int = 2000
    context_truncation_strategy: str = "end"
    
    # Hallucination Detection
    hallucination_threshold_strict: float = 0.6
    hallucination_threshold_balanced: float = 0.4
    hallucination_threshold_default: float = 0.5
    min_statement_length: int = 20
    enable_hallucination_detection: bool = True
    
    # Response Formatting
    image_caption_max_length: int = 120
    show_relevance_scores: bool = False
    show_processing_metadata: bool = False
    
    # Performance Settings
    max_processing_time: int = 120
    enable_response_caching: bool = False
    cache_ttl_minutes: int = 60
    
    def __post_init__(self):
        """Validate configuration after initialization"""
        self.validate()
    
    @classmethod
    def from_environment(cls) -> 'QueryConfiguration':
        """Create configuration from environment variables"""
        config = cls()
        
        # Load from environment variables with fallbacks
        config.retrieval_k = int(os.getenv('RETRIEVAL_K', str(config.retrieval_k)))
        config.relevance_threshold = float(os.getenv('RELEVANCE_THRESHOLD', str(config.relevance_threshold)))
        config.min_documents = int(os.getenv('MIN_DOCUMENTS', str(config.min_documents)))
        config.max_documents = int(os.getenv('MAX_DOCUMENTS', str(config.max_documents)))
        
        # Response display limits
        config.max_pdf_images_display = int(os.getenv('MAX_PDF_IMAGES_DISPLAY', str(config.max_pdf_images_display)))
        config.max_url_images_display = int(os.getenv('MAX_URL_IMAGES_DISPLAY', str(config.max_url_images_display)))
        config.max_tables_display = int(os.getenv('MAX_TABLES_DISPLAY', str(config.max_tables_display)))
        config.max_pdf_links_display = int(os.getenv('MAX_PDF_LINKS_DISPLAY', str(config.max_pdf_links_display)))
        
        # Context processing
        config.max_vision_context_length = int(os.getenv('MAX_VISION_CONTEXT_LENGTH', str(config.max_vision_context_length)))
        config.context_truncation_strategy = os.getenv('CONTEXT_TRUNCATION_STRATEGY', config.context_truncation_strategy)
        
        # Hallucination detection
        config.hallucination_threshold_strict = float(os.getenv('HALLUCINATION_THRESHOLD_STRICT', str(config.hallucination_threshold_strict)))
        config.hallucination_threshold_balanced = float(os.getenv('HALLUCINATION_THRESHOLD_BALANCED', str(config.hallucination_threshold_balanced)))
        config.hallucination_threshold_default = float(os.getenv('HALLUCINATION_THRESHOLD_DEFAULT', str(config.hallucination_threshold_default)))
        config.min_statement_length = int(os.getenv('MIN_STATEMENT_LENGTH', str(config.min_statement_length)))
        config.enable_hallucination_detection = os.getenv('ENABLE_HALLUCINATION_DETECTION', 'true').lower() == 'true'
        
        # Response formatting
        config.image_caption_max_length = int(os.getenv('IMAGE_CAPTION_MAX_LENGTH', str(config.image_caption_max_length)))
        config.show_relevance_scores = os.getenv('SHOW_RELEVANCE_SCORES', 'false').lower() == 'true'
        config.show_processing_metadata = os.getenv('SHOW_PROCESSING_METADATA', 'false').lower() == 'true'
        
        # Performance settings
        config.max_processing_time = int(os.getenv('MAX_PROCESSING_TIME', str(config.max_processing_time)))
        config.enable_response_caching = os.getenv('ENABLE_RESPONSE_CACHING', 'false').lower() == 'true'
        config.cache_ttl_minutes = int(os.getenv('CACHE_TTL_MINUTES', str(config.cache_ttl_minutes)))
        
        config.validate()
        return config
    
    @classmethod
    def from_json_file(cls, file_path: str) -> 'QueryConfiguration':
        """Load configuration from JSON file"""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Extract query_parameters and hallucination_detection sections
            query_params = data.get('query_parameters', {})
            hallucination_params = data.get('hallucination_detection', {})
            
            config = cls()
            
            # Update with values from JSON
            for key, value in query_params.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            # Map hallucination detection parameters
            if 'threshold_strict' in hallucination_params:
                config.hallucination_threshold_strict = hallucination_params['threshold_strict']
            if 'threshold_balanced' in hallucination_params:
                config.hallucination_threshold_balanced = hallucination_params['threshold_balanced']
            if 'threshold_default' in hallucination_params:
                config.hallucination_threshold_default = hallucination_params['threshold_default']
            if 'min_statement_length' in hallucination_params:
                config.min_statement_length = hallucination_params['min_statement_length']
            if 'enable_detection' in hallucination_params:
                config.enable_hallucination_detection = hallucination_params['enable_detection']
            
            config.validate()
            return config
            
        except Exception as e:
            logger.error(f"Failed to load configuration from {file_path}: {str(e)}")
            logger.info("Using default configuration")
            return cls()
    
    def validate(self):
        """Validate all configuration parameters"""
        errors = []
        
        # Validate retrieval parameters
        if not 1 <= self.retrieval_k <= 50:
            errors.append(f"retrieval_k must be between 1 and 50, got {self.retrieval_k}")
        
        if not 0.0 <= self.relevance_threshold <= 1.0:
            errors.append(f"relevance_threshold must be between 0.0 and 1.0, got {self.relevance_threshold}")
        
        if not 1 <= self.min_documents <= 20:
            errors.append(f"min_documents must be between 1 and 20, got {self.min_documents}")
        
        if not 1 <= self.max_documents <= 50:
            errors.append(f"max_documents must be between 1 and 50, got {self.max_documents}")
        
        if self.min_documents > self.max_documents:
            errors.append(f"min_documents ({self.min_documents}) cannot be greater than max_documents ({self.max_documents})")
        
        # Validate display limits
        if not 1 <= self.max_pdf_images_display <= 20:
            errors.append(f"max_pdf_images_display must be between 1 and 20, got {self.max_pdf_images_display}")
        
        if not 1 <= self.max_url_images_display <= 20:
            errors.append(f"max_url_images_display must be between 1 and 20, got {self.max_url_images_display}")
        
        if not 1 <= self.max_tables_display <= 10:
            errors.append(f"max_tables_display must be between 1 and 10, got {self.max_tables_display}")
        
        if not 1 <= self.max_pdf_links_display <= 50:
            errors.append(f"max_pdf_links_display must be between 1 and 50, got {self.max_pdf_links_display}")
        
        # Validate context processing
        if not 100 <= self.max_vision_context_length <= 10000:
            errors.append(f"max_vision_context_length must be between 100 and 10000, got {self.max_vision_context_length}")
        
        if self.context_truncation_strategy not in ['end', 'middle', 'smart']:
            errors.append(f"context_truncation_strategy must be 'end', 'middle', or 'smart', got {self.context_truncation_strategy}")
        
        # Validate hallucination detection
        if not 0.0 <= self.hallucination_threshold_strict <= 1.0:
            errors.append(f"hallucination_threshold_strict must be between 0.0 and 1.0, got {self.hallucination_threshold_strict}")
        
        if not 0.0 <= self.hallucination_threshold_balanced <= 1.0:
            errors.append(f"hallucination_threshold_balanced must be between 0.0 and 1.0, got {self.hallucination_threshold_balanced}")
        
        if not 0.0 <= self.hallucination_threshold_default <= 1.0:
            errors.append(f"hallucination_threshold_default must be between 0.0 and 1.0, got {self.hallucination_threshold_default}")
        
        if not 5 <= self.min_statement_length <= 100:
            errors.append(f"min_statement_length must be between 5 and 100, got {self.min_statement_length}")
        
        # Validate response formatting
        if not 50 <= self.image_caption_max_length <= 500:
            errors.append(f"image_caption_max_length must be between 50 and 500, got {self.image_caption_max_length}")
        
        # Validate performance settings
        if not 10 <= self.max_processing_time <= 600:
            errors.append(f"max_processing_time must be between 10 and 600 seconds, got {self.max_processing_time}")
        
        if not 1 <= self.cache_ttl_minutes <= 1440:
            errors.append(f"cache_ttl_minutes must be between 1 and 1440 minutes, got {self.cache_ttl_minutes}")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
        
        logger.info("Configuration validation passed")
    
    def get_hallucination_threshold(self, mode: str) -> float:
        """Get the appropriate hallucination threshold for the given mode"""
        if not self.enable_hallucination_detection:
            return 0.0
        
        thresholds = {
            'strict': self.hallucination_threshold_strict,
            'balanced': self.hallucination_threshold_balanced,
            'off': 0.0  # No threshold for off mode
        }
        return thresholds.get(mode, self.hallucination_threshold_default)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'query_parameters': {
                'retrieval_k': self.retrieval_k,
                'relevance_threshold': self.relevance_threshold,
                'min_documents': self.min_documents,
                'max_documents': self.max_documents,
                'max_pdf_images_display': self.max_pdf_images_display,
                'max_url_images_display': self.max_url_images_display,
                'max_tables_display': self.max_tables_display,
                'max_pdf_links_display': self.max_pdf_links_display,
                'max_vision_context_length': self.max_vision_context_length,
                'context_truncation_strategy': self.context_truncation_strategy
            },
            'hallucination_detection': {
                'threshold_strict': self.hallucination_threshold_strict,
                'threshold_balanced': self.hallucination_threshold_balanced,
                'threshold_default': self.hallucination_threshold_default,
                'min_statement_length': self.min_statement_length,
                'enable_detection': self.enable_hallucination_detection
            },
            'response_formatting': {
                'image_caption_max_length': self.image_caption_max_length,
                'show_relevance_scores': self.show_relevance_scores,
                'show_processing_metadata': self.show_processing_metadata
            },
            'performance': {
                'max_processing_time': self.max_processing_time,
                'enable_response_caching': self.enable_response_caching,
                'cache_ttl_minutes': self.cache_ttl_minutes
            }
        }


# Global configuration instance
_global_config: Optional[QueryConfiguration] = None


def get_query_config() -> QueryConfiguration:
    """Get the global query configuration instance"""
    global _global_config
    if _global_config is None:
        # Try to load from default models file first, then environment
        try:
            _global_config = QueryConfiguration.from_json_file('config/default_models.json')
        except Exception:
            _global_config = QueryConfiguration.from_environment()
    return _global_config


def reload_query_config():
    """Reload the global configuration"""
    global _global_config
    _global_config = None
    return get_query_config()
