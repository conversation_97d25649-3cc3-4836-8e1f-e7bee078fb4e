#!/usr/bin/env python3
"""
Test suite for the centralized configuration manager

This test suite validates all aspects of the ConfigManager including:
- Configuration loading from multiple sources
- Environment-specific overrides
- Type-safe configuration access
- Validation of required settings
- Error handling for missing configurations
"""

import os
import tempfile
import json
from pathlib import Path
import pytest
from unittest.mock import patch

# Import the configuration manager
from config.config_manager import ConfigManager, get_config, reload_config

class TestConfigManager:
    """Test suite for ConfigManager functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        # Clear any cached config
        reload_config()
    
    def test_config_manager_initialization(self):
        """Test that ConfigManager initializes correctly"""
        config = ConfigManager()
        assert config.environment.value in ['development', 'testing', 'production']
        assert config.config_dir.exists()
    
    def test_environment_detection(self):
        """Test environment detection from FLASK_ENV"""
        # Test default environment
        config = ConfigManager()
        assert config.environment.value == 'development'
        
        # Test explicit environment
        with patch.dict(os.environ, {'FLASK_ENV': 'production'}):
            config = ConfigManager('production')
            assert config.environment.value == 'production'
    
    def test_database_config_loading(self):
        """Test database configuration loading"""
        config = ConfigManager()
        db_config = config.get_database_config()
        
        # Verify required database settings
        assert db_config.main_db_path is not None
        assert db_config.user_db_path is not None
        assert db_config.max_connections > 0
        assert db_config.connection_timeout > 0
        assert db_config.journal_mode in ['WAL', 'DELETE', 'TRUNCATE', 'PERSIST', 'MEMORY', 'OFF']
    
    def test_file_upload_config_loading(self):
        """Test file upload configuration loading"""
        config = ConfigManager()
        file_config = config.get_file_upload_config()
        
        # Verify file upload settings
        assert file_config.upload_folder is not None
        assert file_config.max_content_length > 0
        assert isinstance(file_config.allowed_extensions, set)
        assert 'pdf' in file_config.allowed_extensions
    
    def test_security_config_loading(self):
        """Test security configuration loading"""
        config = ConfigManager()
        security_config = config.get_security_config()
        
        # Verify security settings
        assert security_config.secret_key is not None
        assert len(security_config.secret_key) > 10
        assert security_config.csrf_time_limit > 0
        assert security_config.password_expiry_days > 0
    
    def test_vector_database_config_loading(self):
        """Test vector database configuration loading"""
        config = ConfigManager()
        vector_config = config.get_vector_database_config()
        
        # Verify vector database settings
        assert vector_config.chroma_base_path is not None
        assert vector_config.collection_name is not None
        assert isinstance(vector_config.categories, list)
        assert len(vector_config.categories) > 0
    
    def test_environment_variable_override(self):
        """Test that environment variables override configuration"""
        test_secret = "test-secret-key-from-env"
        test_folder = "/tmp/test-uploads"
        
        with patch.dict(os.environ, {
            'SECRET_KEY': test_secret,
            'TEMP_FOLDER': test_folder
        }):
            config = ConfigManager()
            security_config = config.get_security_config()
            file_config = config.get_file_upload_config()
            
            assert security_config.secret_key == test_secret
            assert file_config.upload_folder == test_folder
    
    def test_dot_notation_access(self):
        """Test configuration access using dot notation"""
        config = ConfigManager()
        
        # Test getting values with dot notation
        secret_key = config.get("security.secret_key")
        assert secret_key is not None
        
        upload_folder = config.get("file_upload.upload_folder")
        assert upload_folder is not None
        
        # Test default values
        non_existent = config.get("non.existent.key", "default_value")
        assert non_existent == "default_value"
    
    def test_configuration_validation(self):
        """Test configuration validation"""
        # This should not raise an exception with valid configuration
        config = ConfigManager()
        
        # Test that required directories are created
        file_config = config.get_file_upload_config()
        upload_path = Path(file_config.upload_folder)
        assert upload_path.exists() or upload_path.parent.exists()
    
    def test_global_config_instance(self):
        """Test global configuration instance"""
        config1 = get_config()
        config2 = get_config()
        
        # Should return the same instance
        assert config1 is config2
        
        # Test reload functionality
        config3 = reload_config()
        assert config3 is not config1

class TestFlaskIntegration:
    """Test Flask integration with ConfigManager"""
    
    def test_flask_config_creation(self):
        """Test Flask configuration creation"""
        from config.settings import DevelopmentConfig, ProductionConfig
        
        # Test development config
        dev_config = DevelopmentConfig()
        assert dev_config.DEBUG is True
        assert dev_config.SECRET_KEY is not None
        assert dev_config.SQLALCHEMY_DATABASE_URI is not None
        
        # Test production config
        prod_config = ProductionConfig()
        assert prod_config.DEBUG is False
        assert prod_config.SESSION_COOKIE_SECURE is True
    
    def test_flask_app_creation(self):
        """Test Flask app creation with new configuration"""
        from app import create_app
        
        # Test development app
        dev_app = create_app('development')
        assert dev_app.config['DEBUG'] is True
        assert dev_app.config['SECRET_KEY'] is not None
        
        # Test that configuration values are accessible
        assert 'UPLOAD_FOLDER' in dev_app.config
        assert 'MAX_CONTENT_LENGTH' in dev_app.config
        assert 'WTF_CSRF_ENABLED' in dev_app.config

def run_configuration_tests():
    """Run all configuration tests"""
    print("Running ConfigManager tests...")
    
    # Test basic functionality
    config = ConfigManager()
    print(f"✓ ConfigManager initialized for environment: {config.environment.value}")
    
    # Test database configuration
    db_config = config.get_database_config()
    print(f"✓ Database config loaded: {db_config.main_db_path}")
    
    # Test file upload configuration
    file_config = config.get_file_upload_config()
    print(f"✓ File upload config loaded: {file_config.upload_folder}")
    
    # Test security configuration
    security_config = config.get_security_config()
    print(f"✓ Security config loaded: secret key length = {len(security_config.secret_key)}")
    
    # Test Flask integration
    from app import create_app
    app = create_app('development')
    print(f"✓ Flask app created successfully with DEBUG = {app.config['DEBUG']}")
    
    print("\nAll configuration tests passed! ✓")

if __name__ == "__main__":
    run_configuration_tests()
