import os
import logging
import requests
import json
import time
from datetime import datetime
import shutil
from bs4 import BeautifulSoup
import fitz  # PyMuPDF

# Import database utilities
from app.utils import content_db as db
from app.services.pdf_processor import (
    process_pdf as original_process_pdf,
    extract_cover_image_from_pdf,
    extract_images_from_pdf,
    extract_links_from_pdf
)
from app.services.embedding_service import scrape_single_url, extract_images_and_links_from_html
from scripts.setup.create_temp_dirs import create_pdf_directory_structure

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./data/temp")

def process_pdf_db_first(pdf_path, category, source_url=None, extract_tables=True, save_images=True, save_tables=True,
                        use_vision=None, filter_sensitivity=None, max_images=None, force_update=False, extract_locations=True):
    """
    Process a PDF file using the database-first approach.
    This function checks if the PDF content already exists in the database before processing.
    """
    logger.info(f"process_pdf_db_first called with pdf_path: {pdf_path}")
    logger.info(f"PDF file exists: {os.path.exists(pdf_path)}")
    
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return {"text": [], "images": [], "tables": [], "links": [], "metadata": {}}
    
    # Get the PDF filename
    pdf_name = os.path.basename(pdf_path)
    logger.info(f"Processing PDF: {pdf_name}")
    
    # Check if this PDF has already been processed and stored in the database
    logger.info(f"Checking if PDF {pdf_name} exists in database...")
    existing_pdf = db.get_pdf_by_filename(pdf_name, category)
    
    if existing_pdf and not force_update:
        logger.info(f"PDF {pdf_name} found in database, but database retrieval not fully implemented yet")
        logger.info(f"Proceeding with original processing for now")
    
    logger.info(f"PDF {pdf_name} not found in database or force_update=True, processing with original function")
    
    # Process the PDF file using the original function
    logger.info(f"Calling original_process_pdf for {pdf_path}")
    pdf_result = original_process_pdf(
        pdf_path,
        category,
        source_url,
        extract_tables=extract_tables,
        save_images=save_images,
        save_tables=save_tables,
        use_vision=use_vision,
        filter_sensitivity=filter_sensitivity,
        max_images=max_images,
        extract_locations=extract_locations
    )
    
    # Debug: Log the PDF processing results
    logger.info(f"Original PDF processing results for {pdf_name}:")
    logger.info(f"  Text pages: {len(pdf_result.get('text', []))}")
    logger.info(f"  Images: {len(pdf_result.get('images', []))}")
    logger.info(f"  Tables: {len(pdf_result.get('tables', []))}")
    logger.info(f"  Links: {len(pdf_result.get('links', []))}")
    logger.info(f"  Metadata keys: {list(pdf_result.get('metadata', {}).keys())}")
    
    if not pdf_result.get('text'):
        logger.error(f"No text extracted from PDF {pdf_name}")
        logger.error(f"PDF result keys: {list(pdf_result.keys())}")
        if 'metadata' in pdf_result:
            logger.error(f"PDF metadata: {pdf_result['metadata']}")
    
    # Store the processed content in the database for future retrieval
    logger.info(f"Storing processed content in database for {pdf_name}")
    try:
        # Create source URL record if provided
        source_url_id = None
        if source_url:
            source_url_id = db.insert_source_url(source_url)
            logger.info(f"Created source URL record with ID: {source_url_id} for URL: {source_url}")
        
        # Create PDF document record
        pdf_id = db.associate_pdf_with_url(pdf_name, category, pdf_name, source_url_id)
        logger.info(f"Created PDF document record with ID: {pdf_id}")
        
        # Add source URL ID to the metadata for use in vector storage
        if source_url_id:
            pdf_result["metadata"]["source_url_id"] = source_url_id
            logger.info(f"Added source_url_id {source_url_id} to PDF metadata")
        
        # Note: Database storage functions for text pages, images, tables, links are not implemented yet
        # For now, we'll just store the basic PDF record and return the processed result
        
        logger.info(f"Successfully stored basic PDF record in database for PDF {pdf_name}")
        
    except Exception as e:
        logger.error(f"Failed to store content in database for {pdf_name}: {str(e)}")
        # Continue with the result even if database storage fails
    
    return pdf_result
