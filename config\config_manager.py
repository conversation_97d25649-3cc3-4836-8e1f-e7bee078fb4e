"""
Centralized Configuration Manager for ERDB Document Management System

This module provides a unified configuration system that:
- Consolidates all application settings
- Supports environment-specific configurations
- Validates configuration values
- Provides type-safe access to settings
"""

import os
import json
import configparser
from pathlib import Path
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class Environment(Enum):
    """Supported deployment environments"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    # Main SQLite database
    main_db_path: str = "./erdb_main.db"
    user_db_path: str = "./user_management.db"
    chat_db_path: str = "./chat_history.db"
    content_db_path: str = "./content_db.sqlite"
    
    # Connection settings
    max_connections: int = 10
    connection_timeout: int = 30
    busy_timeout: int = 30000
    
    # Performance settings
    journal_mode: str = "WAL"
    synchronous: str = "NORMAL"
    cache_size: int = 10000
    temp_store: str = "MEMORY"
    mmap_size: int = 268435456  # 256MB
    foreign_keys: bool = True

@dataclass
class VectorDatabaseConfig:
    """Vector database configuration"""
    chroma_base_path: str = "./data/unified_chroma"
    collection_name: str = "unified_collection"
    categories: list = field(default_factory=lambda: ["CANOPY", "MANUAL", "RISE"])
    metadata_filtering: bool = True
    performance_optimized: bool = True

@dataclass
class FileUploadConfig:
    """File upload and processing configuration"""
    upload_folder: str = "./data/temp"
    max_content_length: int = 25 * 1024 * 1024  # 25MB
    allowed_extensions: set = field(default_factory=lambda: {"pdf"})
    profile_pics_dir: str = "./data/temp/profile_pics"
    max_profile_pic_size: int = 5 * 1024 * 1024  # 5MB
    profile_pic_extensions: set = field(default_factory=lambda: {"png", "jpg", "jpeg", "gif"})

@dataclass
class SecurityConfig:
    """Security and authentication configuration"""
    secret_key: str = "dev-secret-key-change-in-production"
    csrf_enabled: bool = True
    csrf_time_limit: int = 3600  # 1 hour
    password_expiry_days: int = 90
    max_login_attempts: int = 5
    email_verify_expiry: int = 86400  # 24 hours
    password_reset_expiry: int = 3600  # 1 hour
    require_admin_approval: bool = False

@dataclass
class RateLimitConfig:
    """Rate limiting configuration"""
    default_limits: str = "200 per day;50 per hour;10 per minute"
    storage_url: str = "memory://"
    exempt_paths: list = field(default_factory=lambda: ["/static/"])

@dataclass
class BackupConfig:
    """Backup and maintenance configuration"""
    backup_directory: str = "./backups"
    retention_days: int = 30
    auto_backup: bool = True
    compression_level: int = 6
    encrypt_backups: bool = False

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "./logs/erdb.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5

class ConfigManager:
    """
    Centralized configuration manager that loads and validates all application settings
    """
    
    def __init__(self, environment: Optional[str] = None):
        self.environment = Environment(environment or os.getenv("FLASK_ENV", "development"))
        self.config_dir = Path(__file__).parent
        self._config_cache = {}
        self._load_configuration()
    
    def _load_configuration(self):
        """Load configuration from multiple sources with proper precedence"""
        # 1. Load base configuration
        self._load_base_config()
        
        # 2. Load environment-specific overrides
        self._load_environment_config()
        
        # 3. Load environment variables (highest precedence)
        self._load_environment_variables()
        
        # 4. Validate configuration
        self._validate_configuration()
    
    def _load_base_config(self):
        """Load base configuration from config files"""
        # Load from database.ini
        db_config_path = self.config_dir / "database.ini"
        if db_config_path.exists():
            parser = configparser.ConfigParser()
            parser.read(db_config_path)
            self._merge_ini_config(parser)
        
        # Load from default_models.json
        models_config_path = self.config_dir / "default_models.json"
        if models_config_path.exists():
            with open(models_config_path, 'r') as f:
                models_config = json.load(f)
                self._config_cache.update(models_config)
    
    def _load_environment_config(self):
        """Load environment-specific configuration overrides"""
        env_config_path = self.config_dir / "environments" / f"{self.environment.value}.json"
        if env_config_path.exists():
            with open(env_config_path, 'r') as f:
                env_config = json.load(f)
                self._deep_merge(self._config_cache, env_config)
    
    def _load_environment_variables(self):
        """Load configuration from environment variables"""
        env_mappings = {
            "SECRET_KEY": "security.secret_key",
            "DATABASE_URL": "database.main_db_path",
            "TEMP_FOLDER": "file_upload.upload_folder",
            "CHROMA_PATH": "vector_database.chroma_base_path",
            "MAX_CONTENT_LENGTH": "file_upload.max_content_length",
            "REQUIRE_ADMIN_APPROVAL": "security.require_admin_approval",
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                self._set_nested_value(config_path, value)
    
    def _merge_ini_config(self, parser: configparser.ConfigParser):
        """Merge INI configuration into config cache"""
        for section_name in parser.sections():
            section = dict(parser[section_name])
            if section_name not in self._config_cache:
                self._config_cache[section_name] = {}
            self._config_cache[section_name].update(section)
    
    def _deep_merge(self, base: dict, override: dict):
        """Deep merge two dictionaries"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def _set_nested_value(self, path: str, value: Any):
        """Set a nested configuration value using dot notation"""
        keys = path.split('.')
        current = self._config_cache
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value
    
    def _validate_configuration(self):
        """Validate configuration values"""
        # Validate required settings
        required_settings = [
            "security.secret_key",
            "database.main_db_path",
            "file_upload.upload_folder"
        ]
        
        for setting in required_settings:
            if not self.get(setting):
                raise ValueError(f"Required configuration setting '{setting}' is missing")
        
        # Validate file paths exist or can be created
        upload_folder = Path(self.get("file_upload.upload_folder", "./data/temp"))
        upload_folder.mkdir(parents=True, exist_ok=True)
        
        backup_dir = Path(self.get("backup.backup_directory", "./backups"))
        backup_dir.mkdir(parents=True, exist_ok=True)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = key.split('.')
        current = self._config_cache
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration as a typed object"""
        return DatabaseConfig(
            main_db_path=self.get("database.main_db_path", "./erdb_main.db"),
            user_db_path=self.get("database.user_db_path", "./user_management.db"),
            chat_db_path=self.get("database.chat_db_path", "./chat_history.db"),
            content_db_path=self.get("database.content_db_path", "./content_db.sqlite"),
            max_connections=int(self.get("database.max_connections", 10)),
            connection_timeout=int(self.get("database.timeout", 30)),
            busy_timeout=int(self.get("database.busy_timeout", 30000)),
            journal_mode=self.get("database.journal_mode", "WAL"),
            synchronous=self.get("database.synchronous", "NORMAL"),
            cache_size=int(self.get("database.cache_size", 10000)),
            temp_store=self.get("database.temp_store", "MEMORY"),
            mmap_size=int(self.get("database.mmap_size", 268435456)),
            foreign_keys=self.get("database.foreign_keys", "ON") == "ON"
        )
    
    def get_vector_database_config(self) -> VectorDatabaseConfig:
        """Get vector database configuration as a typed object"""
        return VectorDatabaseConfig(
            chroma_base_path=self.get("vector_database.chroma_base_path", "./data/unified_chroma"),
            collection_name=self.get("vector_database.collection_name", "unified_collection"),
            categories=self.get("vector_database.categories", ["CANOPY", "MANUAL", "RISE"]),
            metadata_filtering=self.get("vector_database.metadata_filtering", True),
            performance_optimized=self.get("vector_database.performance_optimized", True)
        )
    
    def get_file_upload_config(self) -> FileUploadConfig:
        """Get file upload configuration as a typed object"""
        return FileUploadConfig(
            upload_folder=self.get("file_upload.upload_folder", "./data/temp"),
            max_content_length=int(self.get("file_upload.max_content_length", 25 * 1024 * 1024)),
            allowed_extensions=set(self.get("file_upload.allowed_extensions", ["pdf"])),
            profile_pics_dir=self.get("file_upload.profile_pics_dir", "./data/temp/profile_pics"),
            max_profile_pic_size=int(self.get("file_upload.max_profile_pic_size", 5 * 1024 * 1024)),
            profile_pic_extensions=set(self.get("file_upload.profile_pic_extensions", ["png", "jpg", "jpeg", "gif"]))
        )
    
    def get_security_config(self) -> SecurityConfig:
        """Get security configuration as a typed object"""
        return SecurityConfig(
            secret_key=self.get("security.secret_key", "dev-secret-key-change-in-production"),
            csrf_enabled=self.get("security.csrf_enabled", True),
            csrf_time_limit=int(self.get("security.csrf_time_limit", 3600)),
            password_expiry_days=int(self.get("security.password_expiry_days", 90)),
            max_login_attempts=int(self.get("security.max_login_attempts", 5)),
            email_verify_expiry=int(self.get("security.email_verify_expiry", 86400)),
            password_reset_expiry=int(self.get("security.password_reset_expiry", 3600)),
            require_admin_approval=self.get("security.require_admin_approval", False)
        )

# Global configuration instance
_config_manager = None

def get_config() -> ConfigManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def reload_config():
    """Reload configuration (useful for testing)"""
    global _config_manager
    _config_manager = None
    return get_config()
