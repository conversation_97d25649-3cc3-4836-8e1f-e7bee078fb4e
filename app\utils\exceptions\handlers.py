"""
Flask Error Handlers

Provides Flask-specific error handling for both web and API requests
with consistent error responses and proper logging.
"""

import logging
from flask import Flask, request, jsonify, render_template, current_app
from werkzeug.exceptions import HTTPException
from typing import Dict, <PERSON>, <PERSON><PERSON>, Union

from .core import ERD<PERSON>x<PERSON>, ERDBError, ErrorContext, ErrorSeverity, handle_error

logger = logging.getLogger(__name__)

def create_error_context() -> ErrorContext:
    """Create error context from current Flask request"""
    context = ErrorContext()
    
    if request:
        context.endpoint = request.endpoint
        context.method = request.method
        context.ip_address = request.remote_addr
        context.user_agent = request.headers.get('User-Agent')
        context.request_id = getattr(request, 'request_id', None)
        
        # Add session info if available
        if hasattr(request, 'session') and request.session:
            context.session_id = request.session.get('session_id')
        
        # Add user info if available
        try:
            from flask_login import current_user
            if current_user and current_user.is_authenticated:
                context.user_id = str(current_user.id)
        except ImportError:
            pass
    
    return context

def is_api_request() -> bool:
    """Determine if the current request is an API request"""
    if not request:
        return False
    
    # Check if request path starts with /api/
    if request.path.startswith('/api/'):
        return True
    
    # Check if request accepts JSON
    if request.is_json or 'application/json' in request.headers.get('Accept', ''):
        return True
    
    # Check content type
    if request.content_type and 'application/json' in request.content_type:
        return True
    
    return False

def format_error_response(error: ERDBError, include_details: bool = False) -> Dict[str, Any]:
    """Format error for JSON response"""
    response = {
        'error': True,
        'error_id': error.error_id,
        'error_code': error.error_code,
        'message': error.user_message or error.message,
        'severity': error.severity.value,
        'timestamp': error.timestamp.isoformat()
    }
    
    if error.suggestions:
        response['suggestions'] = error.suggestions
    
    if include_details and error.details:
        response['details'] = error.details
    
    return response

def handle_api_error(error: Union[Exception, ERDBException]) -> Tuple[Dict[str, Any], int]:
    """Handle error for API requests"""
    context = create_error_context()
    erdb_error = handle_error(error, context)
    
    # Determine HTTP status code
    status_code = 500
    if isinstance(error, HTTPException):
        status_code = error.code
    elif isinstance(error, ERDBException):
        # Map error codes to HTTP status codes
        status_map = {
            'VALIDATION_ERROR': 400,
            'AUTHENTICATION_ERROR': 401,
            'AUTHORIZATION_ERROR': 403,
            'PERMISSION_ERROR': 403,
            'NOT_FOUND': 404,
            'CONFLICT': 409,
            'RATE_LIMIT_EXCEEDED': 429
        }
        status_code = status_map.get(error.error_code, 500)
    
    # Include details in development mode
    include_details = current_app.debug if current_app else False
    
    response = format_error_response(erdb_error, include_details)
    
    return response, status_code

def handle_web_error(error: Union[Exception, ERDBException]) -> str:
    """Handle error for web requests"""
    context = create_error_context()
    erdb_error = handle_error(error, context)
    
    # Determine template and status code
    status_code = 500
    template = 'errors/500.html'
    
    if isinstance(error, HTTPException):
        status_code = error.code
        template = f'errors/{status_code}.html'
    
    try:
        return render_template(
            template,
            error=erdb_error,
            error_id=erdb_error.error_id
        ), status_code
    except Exception:
        # Fallback to basic error page
        return render_template(
            'errors/generic.html',
            error=erdb_error,
            error_id=erdb_error.error_id
        ), status_code

def register_error_handlers(app: Flask):
    """Register error handlers with Flask app"""
    
    @app.errorhandler(ERDBException)
    def handle_erdb_exception(error: ERDBException):
        """Handle ERDB-specific exceptions"""
        if is_api_request():
            return jsonify(*handle_api_error(error))
        else:
            return handle_web_error(error)
    
    @app.errorhandler(400)
    def handle_bad_request(error):
        """Handle 400 Bad Request errors"""
        if is_api_request():
            return jsonify({
                'error': True,
                'error_code': 'BAD_REQUEST',
                'message': 'Invalid request data',
                'status': 400
            }), 400
        else:
            return render_template('errors/400.html'), 400
    
    @app.errorhandler(401)
    def handle_unauthorized(error):
        """Handle 401 Unauthorized errors"""
        if is_api_request():
            return jsonify({
                'error': True,
                'error_code': 'UNAUTHORIZED',
                'message': 'Authentication required',
                'status': 401
            }), 401
        else:
            return render_template('errors/401.html'), 401
    
    @app.errorhandler(403)
    def handle_forbidden(error):
        """Handle 403 Forbidden errors"""
        if is_api_request():
            return jsonify({
                'error': True,
                'error_code': 'FORBIDDEN',
                'message': 'Access denied',
                'status': 403
            }), 403
        else:
            return render_template('errors/403.html'), 403
    
    @app.errorhandler(404)
    def handle_not_found(error):
        """Handle 404 Not Found errors"""
        if is_api_request():
            return jsonify({
                'error': True,
                'error_code': 'NOT_FOUND',
                'message': 'Resource not found',
                'status': 404
            }), 404
        else:
            return render_template('errors/404.html'), 404
    
    @app.errorhandler(429)
    def handle_rate_limit(error):
        """Handle 429 Rate Limit errors"""
        if is_api_request():
            return jsonify({
                'error': True,
                'error_code': 'RATE_LIMIT_EXCEEDED',
                'message': 'Rate limit exceeded. Please try again later.',
                'status': 429
            }), 429
        else:
            return render_template('errors/429.html'), 429
    
    @app.errorhandler(500)
    def handle_internal_error(error):
        """Handle 500 Internal Server Error"""
        if is_api_request():
            response, status = handle_api_error(error)
            return jsonify(response), status
        else:
            return handle_web_error(error)
    
    @app.errorhandler(Exception)
    def handle_generic_exception(error):
        """Handle any unhandled exceptions"""
        logger.exception("Unhandled exception occurred")
        
        if is_api_request():
            response, status = handle_api_error(error)
            return jsonify(response), status
        else:
            return handle_web_error(error)
    
    # Add request ID middleware
    @app.before_request
    def add_request_id():
        """Add unique request ID to each request"""
        import uuid
        request.request_id = str(uuid.uuid4())
    
    # Add error monitoring
    @app.after_request
    def log_request_info(response):
        """Log request information for monitoring"""
        if response.status_code >= 400:
            logger.warning(
                f"HTTP {response.status_code} - {request.method} {request.path}",
                extra={
                    'request_id': getattr(request, 'request_id', None),
                    'status_code': response.status_code,
                    'method': request.method,
                    'path': request.path,
                    'ip_address': request.remote_addr,
                    'user_agent': request.headers.get('User-Agent')
                }
            )
        
        return response
    
    logger.info("Error handlers registered successfully")
