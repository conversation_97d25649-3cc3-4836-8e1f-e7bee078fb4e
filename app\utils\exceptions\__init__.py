"""
Centralized Exception Management for ERDB System

This package provides a comprehensive exception hierarchy and handling system for:
- Domain-specific exceptions
- Structured error responses
- Error logging and monitoring
- User-friendly error messages
"""

from .core import (
    ERDBException, 
    ERDBError,
    ErrorSeverity,
    ErrorContext,
    ErrorHandler
)

from .database_exceptions import (
    DatabaseError,
    ConnectionError,
    QueryError,
    TransactionError,
    IntegrityError
)

from .validation_exceptions import (
    ValidationError,
    FileValidationError,
    InputValidationError,
    SecurityValidationError
)

from .auth_exceptions import (
    AuthenticationError,
    AuthorizationError,
    PermissionError,
    SessionError
)

from .business_exceptions import (
    DocumentError,
    EmbeddingError,
    QueryProcessingError,
    ConfigurationError
)

from .handlers import (
    register_error_handlers,
    handle_api_error,
    handle_web_error,
    format_error_response
)

__all__ = [
    # Core
    'ERDBException',
    'ERDBError', 
    'ErrorSeverity',
    'ErrorContext',
    'ErrorHandler',
    
    # Database
    'DatabaseError',
    'ConnectionError',
    'QueryError', 
    'TransactionError',
    'IntegrityError',
    
    # Validation
    'ValidationError',
    'FileValidationError',
    'InputValidationError',
    'SecurityValidationError',
    
    # Authentication
    'AuthenticationError',
    'AuthorizationError',
    'PermissionError',
    'SessionError',
    
    # Business Logic
    'DocumentError',
    'EmbeddingError',
    'QueryProcessingError',
    'ConfigurationError',
    
    # Handlers
    'register_error_handlers',
    'handle_api_error',
    'handle_web_error',
    'format_error_response'
]
