"""
File Upload Validation

Provides comprehensive validation for file uploads including:
- File type validation
- Content validation
- Size limits
- Security scanning
- PDF-specific validation
"""

import os
import hashlib
import mimetypes
from pathlib import Path
from typing import Any, Dict, List, Optional, Set
import magic
import PyPDF2
from PIL import Image
from werkzeug.datastructures import FileStorage

from .core import BaseValidator, ValidationResult, ValidationSeverity
from config.config_manager import get_config

class FileUploadValidator(BaseValidator):
    """
    Base file upload validator with common security checks
    """
    
    def __init__(self, name: str = "file_upload", config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.app_config = get_config()
        self.file_config = self.app_config.get_file_upload_config()
        
        # Default allowed MIME types
        self.allowed_mime_types = self.config.get('allowed_mime_types', {
            'application/pdf',
            'image/jpeg',
            'image/png',
            'image/gif'
        })
        
        # Maximum file size (default from config)
        self.max_size = self.config.get('max_size', self.file_config.max_content_length)
        
        # Dangerous file extensions to block
        self.blocked_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
            '.jar', '.app', '.deb', '.pkg', '.dmg', '.msi', '.run'
        }
    
    def validate(self, data: Any, context: Optional[Dict[str, Any]] = None) -> ValidationResult:
        """Validate file upload"""
        result = ValidationResult(is_valid=True)
        
        if not isinstance(data, FileStorage):
            result.add_issue(
                ValidationSeverity.ERROR,
                "Invalid file object provided",
                code="INVALID_FILE_OBJECT"
            )
            return result
        
        # Check if file was actually uploaded
        if not data.filename:
            result.add_issue(
                ValidationSeverity.ERROR,
                "No file selected for upload",
                code="NO_FILE_SELECTED"
            )
            return result
        
        # Validate filename
        self._validate_filename(data.filename, result)
        
        # Validate file size
        self._validate_file_size(data, result)
        
        # Validate file type
        self._validate_file_type(data, result)
        
        # Validate file content
        self._validate_file_content(data, result)
        
        return result
    
    def _validate_filename(self, filename: str, result: ValidationResult):
        """Validate filename for security issues"""
        # Check for dangerous extensions
        file_ext = Path(filename).suffix.lower()
        if file_ext in self.blocked_extensions:
            result.add_issue(
                ValidationSeverity.ERROR,
                f"File type '{file_ext}' is not allowed for security reasons",
                code="BLOCKED_EXTENSION"
            )
        
        # Check for path traversal attempts
        if '..' in filename or '/' in filename or '\\' in filename:
            result.add_issue(
                ValidationSeverity.ERROR,
                "Filename contains invalid characters",
                code="INVALID_FILENAME"
            )
        
        # Check filename length
        if len(filename) > 255:
            result.add_issue(
                ValidationSeverity.ERROR,
                "Filename is too long (maximum 255 characters)",
                code="FILENAME_TOO_LONG"
            )
        
        # Check for null bytes
        if '\x00' in filename:
            result.add_issue(
                ValidationSeverity.ERROR,
                "Filename contains null bytes",
                code="NULL_BYTE_FILENAME"
            )
    
    def _validate_file_size(self, file_obj: FileStorage, result: ValidationResult):
        """Validate file size"""
        # Seek to end to get file size
        file_obj.seek(0, 2)
        file_size = file_obj.tell()
        file_obj.seek(0)  # Reset to beginning
        
        if file_size == 0:
            result.add_issue(
                ValidationSeverity.ERROR,
                "File is empty",
                code="EMPTY_FILE"
            )
        elif file_size > self.max_size:
            result.add_issue(
                ValidationSeverity.ERROR,
                f"File size ({file_size} bytes) exceeds maximum allowed size ({self.max_size} bytes)",
                code="FILE_TOO_LARGE"
            )
        
        result.metadata['file_size'] = file_size
    
    def _validate_file_type(self, file_obj: FileStorage, result: ValidationResult):
        """Validate file type using multiple methods"""
        filename = file_obj.filename
        
        # Check MIME type from file content
        file_obj.seek(0)
        file_content = file_obj.read(1024)  # Read first 1KB for type detection
        file_obj.seek(0)  # Reset
        
        try:
            detected_mime = magic.from_buffer(file_content, mime=True)
            result.metadata['detected_mime_type'] = detected_mime
            
            if detected_mime not in self.allowed_mime_types:
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"File type '{detected_mime}' is not allowed",
                    code="INVALID_MIME_TYPE"
                )
        except Exception as e:
            result.add_issue(
                ValidationSeverity.WARNING,
                f"Could not detect file type: {str(e)}",
                code="MIME_DETECTION_FAILED"
            )
        
        # Check file extension
        file_ext = Path(filename).suffix.lower()
        guessed_mime, _ = mimetypes.guess_type(filename)
        
        if guessed_mime and guessed_mime not in self.allowed_mime_types:
            result.add_issue(
                ValidationSeverity.WARNING,
                f"File extension suggests type '{guessed_mime}' which may not be allowed",
                code="SUSPICIOUS_EXTENSION"
            )
    
    def _validate_file_content(self, file_obj: FileStorage, result: ValidationResult):
        """Validate file content for malicious patterns"""
        file_obj.seek(0)
        content = file_obj.read(8192)  # Read first 8KB
        file_obj.seek(0)  # Reset
        
        # Check for embedded executables
        executable_signatures = [
            b'MZ',  # DOS/Windows executable
            b'\x7fELF',  # Linux executable
            b'\xca\xfe\xba\xbe',  # Java class file
            b'PK\x03\x04',  # ZIP file (could contain executables)
        ]
        
        for signature in executable_signatures:
            if signature in content:
                result.add_issue(
                    ValidationSeverity.WARNING,
                    "File may contain executable content",
                    code="POSSIBLE_EXECUTABLE"
                )
                break
        
        # Check for script content in non-script files
        script_patterns = [
            b'<script',
            b'javascript:',
            b'vbscript:',
            b'<?php',
            b'#!/bin/',
        ]
        
        for pattern in script_patterns:
            if pattern.lower() in content.lower():
                result.add_issue(
                    ValidationSeverity.WARNING,
                    "File may contain script content",
                    code="POSSIBLE_SCRIPT"
                )
                break

class PDFValidator(FileUploadValidator):
    """
    Specialized validator for PDF files
    """
    
    def __init__(self, name: str = "pdf_validator", config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.allowed_mime_types = {'application/pdf'}
        self.max_pages = self.config.get('max_pages', 1000)
    
    def validate(self, data: Any, context: Optional[Dict[str, Any]] = None) -> ValidationResult:
        """Validate PDF file"""
        result = super().validate(data, context)
        
        if result.is_valid:
            self._validate_pdf_content(data, result)
        
        return result
    
    def _validate_pdf_content(self, file_obj: FileStorage, result: ValidationResult):
        """Validate PDF-specific content"""
        try:
            file_obj.seek(0)
            pdf_reader = PyPDF2.PdfReader(file_obj)
            file_obj.seek(0)  # Reset
            
            # Check page count
            page_count = len(pdf_reader.pages)
            result.metadata['page_count'] = page_count
            
            if page_count > self.max_pages:
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"PDF has too many pages ({page_count}), maximum allowed is {self.max_pages}",
                    code="TOO_MANY_PAGES"
                )
            
            # Check for encryption
            if pdf_reader.is_encrypted:
                result.add_issue(
                    ValidationSeverity.WARNING,
                    "PDF is encrypted",
                    code="ENCRYPTED_PDF"
                )
            
            # Check for JavaScript
            for page_num, page in enumerate(pdf_reader.pages):
                if '/JS' in str(page) or '/JavaScript' in str(page):
                    result.add_issue(
                        ValidationSeverity.WARNING,
                        f"PDF contains JavaScript on page {page_num + 1}",
                        code="PDF_JAVASCRIPT"
                    )
                    break
            
            # Check for forms
            if '/AcroForm' in str(pdf_reader.trailer):
                result.add_issue(
                    ValidationSeverity.INFO,
                    "PDF contains interactive forms",
                    code="PDF_FORMS"
                )
            
        except Exception as e:
            result.add_issue(
                ValidationSeverity.ERROR,
                f"Failed to validate PDF content: {str(e)}",
                code="PDF_VALIDATION_ERROR"
            )

class ImageValidator(FileUploadValidator):
    """
    Specialized validator for image files
    """
    
    def __init__(self, name: str = "image_validator", config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.allowed_mime_types = {
            'image/jpeg',
            'image/png', 
            'image/gif',
            'image/webp'
        }
        self.max_dimensions = self.config.get('max_dimensions', (4096, 4096))
        self.min_dimensions = self.config.get('min_dimensions', (1, 1))
    
    def validate(self, data: Any, context: Optional[Dict[str, Any]] = None) -> ValidationResult:
        """Validate image file"""
        result = super().validate(data, context)
        
        if result.is_valid:
            self._validate_image_content(data, result)
        
        return result
    
    def _validate_image_content(self, file_obj: FileStorage, result: ValidationResult):
        """Validate image-specific content"""
        try:
            file_obj.seek(0)
            with Image.open(file_obj) as img:
                # Check dimensions
                width, height = img.size
                result.metadata['dimensions'] = {'width': width, 'height': height}
                
                max_width, max_height = self.max_dimensions
                min_width, min_height = self.min_dimensions
                
                if width > max_width or height > max_height:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Image dimensions ({width}x{height}) exceed maximum allowed ({max_width}x{max_height})",
                        code="IMAGE_TOO_LARGE"
                    )
                
                if width < min_width or height < min_height:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Image dimensions ({width}x{height}) below minimum required ({min_width}x{min_height})",
                        code="IMAGE_TOO_SMALL"
                    )
                
                # Check for EXIF data
                if hasattr(img, '_getexif') and img._getexif():
                    result.add_issue(
                        ValidationSeverity.INFO,
                        "Image contains EXIF metadata",
                        code="EXIF_DATA_PRESENT"
                    )
            
            file_obj.seek(0)  # Reset
            
        except Exception as e:
            result.add_issue(
                ValidationSeverity.ERROR,
                f"Failed to validate image content: {str(e)}",
                code="IMAGE_VALIDATION_ERROR"
            )
