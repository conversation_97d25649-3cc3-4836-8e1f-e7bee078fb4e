"""
Core exception handling framework

Provides base exception classes and error handling utilities
for consistent error management across the ERDB system.
"""

import logging
import traceback
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ErrorContext:
    """Context information for errors"""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    endpoint: Optional[str] = None
    method: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    additional_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ERDBError:
    """Structured error information"""
    error_id: str
    error_code: str
    message: str
    severity: ErrorSeverity
    context: ErrorContext
    details: Dict[str, Any] = field(default_factory=dict)
    user_message: Optional[str] = None
    suggestions: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for JSON serialization"""
        return {
            'error_id': self.error_id,
            'error_code': self.error_code,
            'message': self.message,
            'severity': self.severity.value,
            'user_message': self.user_message,
            'suggestions': self.suggestions,
            'timestamp': self.timestamp.isoformat(),
            'details': self.details
        }

class ERDBException(Exception):
    """
    Base exception class for all ERDB-specific exceptions
    
    Provides structured error information and context
    """
    
    def __init__(
        self,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        user_message: Optional[str] = None,
        suggestions: Optional[List[str]] = None,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[ErrorContext] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        
        self.error_id = str(uuid.uuid4())
        self.error_code = error_code
        self.severity = severity
        self.user_message = user_message or self._generate_user_message()
        self.suggestions = suggestions or []
        self.details = details or {}
        self.context = context or ErrorContext()
        self.cause = cause
        
        # Log the error
        self._log_error()
    
    def _generate_user_message(self) -> str:
        """Generate a user-friendly error message"""
        # Override in subclasses for specific user messages
        return "An error occurred while processing your request. Please try again."
    
    def _log_error(self):
        """Log the error with appropriate level based on severity"""
        log_data = {
            'error_id': self.error_id,
            'error_code': self.error_code,
            'message': str(self),
            'severity': self.severity.value,
            'details': self.details,
            'context': self.context.__dict__
        }
        
        if self.cause:
            log_data['cause'] = str(self.cause)
            log_data['traceback'] = traceback.format_exception(
                type(self.cause), self.cause, self.cause.__traceback__
            )
        
        if self.severity == ErrorSeverity.CRITICAL:
            logger.critical("Critical error occurred", extra=log_data)
        elif self.severity == ErrorSeverity.HIGH:
            logger.error("High severity error occurred", extra=log_data)
        elif self.severity == ErrorSeverity.MEDIUM:
            logger.warning("Medium severity error occurred", extra=log_data)
        else:
            logger.info("Low severity error occurred", extra=log_data)
    
    def to_error(self) -> ERDBError:
        """Convert exception to ERDBError object"""
        return ERDBError(
            error_id=self.error_id,
            error_code=self.error_code,
            message=str(self),
            severity=self.severity,
            context=self.context,
            details=self.details,
            user_message=self.user_message,
            suggestions=self.suggestions
        )
    
    def add_context(self, **kwargs):
        """Add context information to the error"""
        for key, value in kwargs.items():
            if hasattr(self.context, key):
                setattr(self.context, key, value)
            else:
                self.context.additional_data[key] = value
    
    def add_suggestion(self, suggestion: str):
        """Add a suggestion for resolving the error"""
        self.suggestions.append(suggestion)
    
    def add_detail(self, key: str, value: Any):
        """Add detail information to the error"""
        self.details[key] = value

class ErrorHandler:
    """
    Centralized error handler for processing and formatting errors
    """
    
    def __init__(self):
        self.error_counts = {}
        self.error_history = []
        self.max_history = 1000
    
    def handle_error(
        self,
        error: Union[Exception, ERDBException],
        context: Optional[ErrorContext] = None
    ) -> ERDBError:
        """
        Handle an error and return structured error information
        
        Args:
            error: The exception to handle
            context: Additional context information
            
        Returns:
            ERDBError object with structured error information
        """
        if isinstance(error, ERDBException):
            # Update context if provided
            if context:
                for key, value in context.__dict__.items():
                    if value is not None:
                        setattr(error.context, key, value)
            
            erdb_error = error.to_error()
        else:
            # Convert generic exception to ERDBException
            erdb_error = self._convert_generic_error(error, context)
        
        # Track error statistics
        self._track_error(erdb_error)
        
        return erdb_error
    
    def _convert_generic_error(
        self,
        error: Exception,
        context: Optional[ErrorContext] = None
    ) -> ERDBError:
        """Convert a generic exception to ERDBError"""
        error_code = f"{type(error).__name__}".upper()
        severity = self._determine_severity(error)
        
        erdb_exception = ERDBException(
            message=str(error),
            error_code=error_code,
            severity=severity,
            context=context or ErrorContext(),
            cause=error
        )
        
        return erdb_exception.to_error()
    
    def _determine_severity(self, error: Exception) -> ErrorSeverity:
        """Determine error severity based on exception type"""
        critical_errors = (
            SystemError,
            MemoryError,
            KeyboardInterrupt,
            SystemExit
        )
        
        high_errors = (
            ConnectionError,
            PermissionError,
            FileNotFoundError
        )
        
        if isinstance(error, critical_errors):
            return ErrorSeverity.CRITICAL
        elif isinstance(error, high_errors):
            return ErrorSeverity.HIGH
        else:
            return ErrorSeverity.MEDIUM
    
    def _track_error(self, error: ERDBError):
        """Track error statistics"""
        # Count errors by code
        self.error_counts[error.error_code] = (
            self.error_counts.get(error.error_code, 0) + 1
        )
        
        # Add to history
        self.error_history.append(error)
        
        # Trim history if too long
        if len(self.error_history) > self.max_history:
            self.error_history = self.error_history[-self.max_history:]
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        total_errors = sum(self.error_counts.values())
        
        return {
            'total_errors': total_errors,
            'error_counts': self.error_counts.copy(),
            'recent_errors': len(self.error_history),
            'top_errors': sorted(
                self.error_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]
        }

# Global error handler instance
_error_handler = ErrorHandler()

def get_error_handler() -> ErrorHandler:
    """Get the global error handler"""
    return _error_handler

def handle_error(
    error: Union[Exception, ERDBException],
    context: Optional[ErrorContext] = None
) -> ERDBError:
    """Handle an error using the global error handler"""
    return _error_handler.handle_error(error, context)
