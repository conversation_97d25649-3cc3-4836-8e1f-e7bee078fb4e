"""
Enhanced Database Connection Management for ERDB System

This module provides improved database connection handling with:
- Centralized configuration integration
- Connection monitoring and leak detection
- Enhanced error handling and recovery
- Performance metrics collection
- Automatic connection cleanup
"""

import sqlite3
import threading
import time
import logging
from contextlib import contextmanager
from typing import Generator, Dict, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import weakref

from config.config_manager import get_config

logger = logging.getLogger(__name__)

@dataclass
class ConnectionMetrics:
    """Tracks connection usage metrics"""
    total_connections: int = 0
    active_connections: int = 0
    peak_connections: int = 0
    total_queries: int = 0
    failed_connections: int = 0
    connection_errors: Dict[str, int] = field(default_factory=dict)
    last_reset: datetime = field(default_factory=datetime.now)

@dataclass
class ConnectionInfo:
    """Information about an active connection"""
    connection_id: str
    created_at: datetime
    last_used: datetime
    query_count: int = 0
    thread_id: int = field(default_factory=lambda: threading.get_ident())

class DatabaseConnectionManager:
    """
    Enhanced database connection manager with monitoring and leak detection
    """
    
    def __init__(self):
        self.config = get_config()
        self.db_config = self.config.get_database_config()
        
        # Connection pools for different databases
        self._pools = {}
        self._pool_locks = {}
        self._connection_info = {}
        self._metrics = ConnectionMetrics()
        self._lock = threading.Lock()
        
        # Initialize pools for each database
        self._initialize_pools()
        
        # Start monitoring thread
        self._start_monitoring()
    
    def _initialize_pools(self):
        """Initialize connection pools for all configured databases"""
        databases = {
            'main': self.db_config.main_db_path,
            'user': self.db_config.user_db_path,
            'chat': self.db_config.chat_db_path,
            'content': self.db_config.content_db_path
        }
        
        for db_name, db_path in databases.items():
            self._pools[db_name] = []
            self._pool_locks[db_name] = threading.Lock()
            logger.info(f"Initialized connection pool for {db_name} database: {db_path}")
    
    def _start_monitoring(self):
        """Start background monitoring thread"""
        def monitor():
            while True:
                try:
                    self._cleanup_stale_connections()
                    self._log_metrics()
                    time.sleep(60)  # Check every minute
                except Exception as e:
                    logger.error(f"Error in connection monitoring: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        logger.info("Started database connection monitoring")
    
    def _cleanup_stale_connections(self):
        """Clean up stale connections that haven't been used recently"""
        cutoff_time = datetime.now() - timedelta(minutes=30)
        
        with self._lock:
            stale_connections = [
                conn_id for conn_id, info in self._connection_info.items()
                if info.last_used < cutoff_time
            ]
            
            for conn_id in stale_connections:
                try:
                    # Connection will be cleaned up by weakref callback
                    del self._connection_info[conn_id]
                    logger.debug(f"Cleaned up stale connection: {conn_id}")
                except Exception as e:
                    logger.error(f"Error cleaning up connection {conn_id}: {e}")
    
    def _log_metrics(self):
        """Log connection metrics"""
        with self._lock:
            logger.info(
                f"DB Metrics - Active: {self._metrics.active_connections}, "
                f"Peak: {self._metrics.peak_connections}, "
                f"Total: {self._metrics.total_connections}, "
                f"Queries: {self._metrics.total_queries}, "
                f"Errors: {self._metrics.failed_connections}"
            )
    
    def _create_connection(self, db_path: str) -> sqlite3.Connection:
        """Create a new database connection with optimizations"""
        try:
            connection = sqlite3.connect(
                db_path,
                timeout=self.db_config.connection_timeout,
                check_same_thread=False
            )
            
            # Set row factory for dict-like access
            connection.row_factory = self._dict_factory
            
            # Apply performance optimizations
            self._optimize_connection(connection)
            
            # Track connection
            conn_id = str(id(connection))
            self._connection_info[conn_id] = ConnectionInfo(
                connection_id=conn_id,
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            
            # Set up cleanup callback
            weakref.finalize(connection, self._connection_cleanup, conn_id)
            
            with self._lock:
                self._metrics.total_connections += 1
                self._metrics.active_connections += 1
                self._metrics.peak_connections = max(
                    self._metrics.peak_connections,
                    self._metrics.active_connections
                )
            
            logger.debug(f"Created new database connection: {conn_id}")
            return connection
            
        except sqlite3.Error as e:
            with self._lock:
                self._metrics.failed_connections += 1
                error_type = type(e).__name__
                self._metrics.connection_errors[error_type] = (
                    self._metrics.connection_errors.get(error_type, 0) + 1
                )
            
            logger.error(f"Failed to create database connection to {db_path}: {e}")
            raise
    
    def _connection_cleanup(self, conn_id: str):
        """Cleanup callback for when connection is garbage collected"""
        with self._lock:
            self._metrics.active_connections = max(0, self._metrics.active_connections - 1)
            if conn_id in self._connection_info:
                del self._connection_info[conn_id]
        
        logger.debug(f"Connection {conn_id} cleaned up")
    
    def _dict_factory(self, cursor: sqlite3.Cursor, row: tuple) -> dict:
        """Convert database row objects to dictionaries"""
        return {col[0]: row[idx] for idx, col in enumerate(cursor.description)}
    
    def _optimize_connection(self, connection: sqlite3.Connection):
        """Apply performance optimizations to a connection"""
        optimizations = [
            f"PRAGMA journal_mode = {self.db_config.journal_mode}",
            f"PRAGMA synchronous = {self.db_config.synchronous}",
            f"PRAGMA cache_size = {self.db_config.cache_size}",
            f"PRAGMA temp_store = {self.db_config.temp_store}",
            f"PRAGMA mmap_size = {self.db_config.mmap_size}",
            f"PRAGMA foreign_keys = {'ON' if self.db_config.foreign_keys else 'OFF'}",
            f"PRAGMA busy_timeout = {self.db_config.busy_timeout}"
        ]
        
        for pragma in optimizations:
            try:
                connection.execute(pragma)
            except sqlite3.Error as e:
                logger.warning(f"Failed to apply optimization '{pragma}': {e}")
    
    def _update_connection_usage(self, connection: sqlite3.Connection):
        """Update connection usage statistics"""
        conn_id = str(id(connection))
        if conn_id in self._connection_info:
            info = self._connection_info[conn_id]
            info.last_used = datetime.now()
            info.query_count += 1
            
            with self._lock:
                self._metrics.total_queries += 1
    
    @contextmanager
    def get_connection(self, database: str = 'user') -> Generator[sqlite3.Connection, None, None]:
        """
        Get a database connection with automatic cleanup
        
        Args:
            database: Database name ('main', 'user', 'chat', 'content')
        
        Yields:
            A SQLite connection object
        """
        # Map database names to paths
        db_paths = {
            'main': self.db_config.main_db_path,
            'user': self.db_config.user_db_path,
            'chat': self.db_config.chat_db_path,
            'content': self.db_config.content_db_path
        }
        
        if database not in db_paths:
            raise ValueError(f"Unknown database: {database}")
        
        db_path = db_paths[database]
        connection = None
        
        try:
            connection = self._create_connection(db_path)
            yield connection
        finally:
            if connection:
                try:
                    connection.close()
                except sqlite3.Error as e:
                    logger.error(f"Error closing connection: {e}")
    
    @contextmanager
    def get_transaction(self, database: str = 'user') -> Generator[sqlite3.Connection, None, None]:
        """
        Get a database connection with automatic transaction management
        
        Args:
            database: Database name ('main', 'user', 'chat', 'content')
        
        Yields:
            A SQLite connection object with an active transaction
        """
        with self.get_connection(database) as connection:
            try:
                connection.execute("BEGIN TRANSACTION")
                yield connection
                connection.commit()
            except Exception as e:
                connection.rollback()
                logger.error(f"Transaction failed, rolled back: {e}")
                raise
    
    def execute_query(self, query: str, params: tuple = (), database: str = 'user') -> list:
        """
        Execute a query and return results
        
        Args:
            query: SQL query to execute
            params: Query parameters
            database: Database name
        
        Returns:
            List of result rows as dictionaries
        """
        with self.get_connection(database) as connection:
            self._update_connection_usage(connection)
            cursor = connection.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = (), database: str = 'user') -> int:
        """
        Execute an update/insert/delete query
        
        Args:
            query: SQL query to execute
            params: Query parameters
            database: Database name
        
        Returns:
            Number of affected rows
        """
        with self.get_transaction(database) as connection:
            self._update_connection_usage(connection)
            cursor = connection.cursor()
            cursor.execute(query, params)
            return cursor.rowcount
    
    def get_metrics(self) -> ConnectionMetrics:
        """Get current connection metrics"""
        return self._metrics
    
    def reset_metrics(self):
        """Reset connection metrics"""
        with self._lock:
            self._metrics = ConnectionMetrics()

# Global connection manager instance
_connection_manager = None

def get_connection_manager() -> DatabaseConnectionManager:
    """Get the global database connection manager"""
    global _connection_manager
    if _connection_manager is None:
        _connection_manager = DatabaseConnectionManager()
    return _connection_manager

# Convenience functions for backward compatibility
def db_connection(database: str = 'user'):
    """Get a database connection context manager"""
    return get_connection_manager().get_connection(database)

def db_transaction(database: str = 'user'):
    """Get a database transaction context manager"""
    return get_connection_manager().get_transaction(database)

def execute_query(query: str, params: tuple = (), database: str = 'user') -> list:
    """Execute a query and return results"""
    return get_connection_manager().execute_query(query, params, database)

def execute_update(query: str, params: tuple = (), database: str = 'user') -> int:
    """Execute an update query and return affected row count"""
    return get_connection_manager().execute_update(query, params, database)
