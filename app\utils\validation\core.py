"""
Core validation framework components

Provides base classes and utilities for building a comprehensive
validation system with consistent error handling and reporting.
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union
from enum import Enum

logger = logging.getLogger(__name__)

class ValidationSeverity(Enum):
    """Severity levels for validation issues"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class ValidationIssue:
    """Represents a single validation issue"""
    severity: ValidationSeverity
    message: str
    field: Optional[str] = None
    code: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ValidationResult:
    """Result of a validation operation"""
    is_valid: bool
    issues: List[ValidationIssue] = field(default_factory=list)
    sanitized_data: Optional[Any] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_issue(self, severity: ValidationSeverity, message: str, 
                  field: Optional[str] = None, code: Optional[str] = None,
                  details: Optional[Dict[str, Any]] = None):
        """Add a validation issue"""
        issue = ValidationIssue(
            severity=severity,
            message=message,
            field=field,
            code=code,
            details=details or {}
        )
        self.issues.append(issue)
        
        # Mark as invalid if we have errors or critical issues
        if severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL]:
            self.is_valid = False
    
    def has_errors(self) -> bool:
        """Check if result has any errors or critical issues"""
        return any(issue.severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL] 
                  for issue in self.issues)
    
    def get_errors(self) -> List[ValidationIssue]:
        """Get all error and critical issues"""
        return [issue for issue in self.issues 
                if issue.severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL]]
    
    def get_warnings(self) -> List[ValidationIssue]:
        """Get all warning issues"""
        return [issue for issue in self.issues 
                if issue.severity == ValidationSeverity.WARNING]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for JSON serialization"""
        return {
            'is_valid': self.is_valid,
            'issues': [
                {
                    'severity': issue.severity.value,
                    'message': issue.message,
                    'field': issue.field,
                    'code': issue.code,
                    'details': issue.details
                }
                for issue in self.issues
            ],
            'metadata': self.metadata
        }

class ValidationError(Exception):
    """Exception raised when validation fails"""
    
    def __init__(self, message: str, result: Optional[ValidationResult] = None):
        super().__init__(message)
        self.result = result or ValidationResult(is_valid=False)
        if not self.result.issues:
            self.result.add_issue(ValidationSeverity.ERROR, message)

class BaseValidator(ABC):
    """
    Base class for all validators
    
    Provides common functionality and enforces consistent interface
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def validate(self, data: Any, context: Optional[Dict[str, Any]] = None) -> ValidationResult:
        """
        Validate the provided data
        
        Args:
            data: Data to validate
            context: Additional context for validation
            
        Returns:
            ValidationResult with validation outcome
        """
        pass
    
    def sanitize(self, data: Any, context: Optional[Dict[str, Any]] = None) -> Any:
        """
        Sanitize the provided data (optional override)
        
        Args:
            data: Data to sanitize
            context: Additional context for sanitization
            
        Returns:
            Sanitized data
        """
        return data
    
    def validate_and_sanitize(self, data: Any, context: Optional[Dict[str, Any]] = None) -> ValidationResult:
        """
        Validate and sanitize data in one operation
        
        Args:
            data: Data to validate and sanitize
            context: Additional context
            
        Returns:
            ValidationResult with sanitized data if valid
        """
        result = self.validate(data, context)
        
        if result.is_valid:
            try:
                result.sanitized_data = self.sanitize(data, context)
            except Exception as e:
                self.logger.error(f"Sanitization failed: {e}")
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Sanitization failed: {str(e)}",
                    code="SANITIZATION_ERROR"
                )
        
        return result

class CompositeValidator(BaseValidator):
    """
    Validator that combines multiple validators
    
    Runs all validators and aggregates results
    """
    
    def __init__(self, name: str, validators: List[BaseValidator], 
                 stop_on_first_error: bool = False):
        super().__init__(name)
        self.validators = validators
        self.stop_on_first_error = stop_on_first_error
    
    def validate(self, data: Any, context: Optional[Dict[str, Any]] = None) -> ValidationResult:
        """Run all validators and aggregate results"""
        result = ValidationResult(is_valid=True)
        
        for validator in self.validators:
            try:
                validator_result = validator.validate(data, context)
                
                # Merge issues
                result.issues.extend(validator_result.issues)
                
                # Update validity
                if not validator_result.is_valid:
                    result.is_valid = False
                
                # Merge metadata
                result.metadata.update(validator_result.metadata)
                
                # Stop on first error if configured
                if self.stop_on_first_error and not validator_result.is_valid:
                    break
                    
            except Exception as e:
                self.logger.error(f"Validator {validator.name} failed: {e}")
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Validator {validator.name} failed: {str(e)}",
                    code="VALIDATOR_ERROR"
                )
                
                if self.stop_on_first_error:
                    break
        
        return result

class ValidationRegistry:
    """
    Registry for managing validators
    
    Allows registration and retrieval of validators by name
    """
    
    def __init__(self):
        self._validators: Dict[str, BaseValidator] = {}
    
    def register(self, validator: BaseValidator):
        """Register a validator"""
        self._validators[validator.name] = validator
        logger.info(f"Registered validator: {validator.name}")
    
    def get(self, name: str) -> Optional[BaseValidator]:
        """Get a validator by name"""
        return self._validators.get(name)
    
    def get_all(self) -> Dict[str, BaseValidator]:
        """Get all registered validators"""
        return self._validators.copy()
    
    def validate_with(self, validator_name: str, data: Any, 
                     context: Optional[Dict[str, Any]] = None) -> ValidationResult:
        """Validate data using a named validator"""
        validator = self.get(validator_name)
        if not validator:
            raise ValueError(f"Validator '{validator_name}' not found")
        
        return validator.validate(data, context)

# Global validator registry
_registry = ValidationRegistry()

def get_validator_registry() -> ValidationRegistry:
    """Get the global validator registry"""
    return _registry

def register_validator(validator: BaseValidator):
    """Register a validator in the global registry"""
    _registry.register(validator)

def validate_with(validator_name: str, data: Any, 
                 context: Optional[Dict[str, Any]] = None) -> ValidationResult:
    """Validate data using a named validator from the global registry"""
    return _registry.validate_with(validator_name, data, context)
