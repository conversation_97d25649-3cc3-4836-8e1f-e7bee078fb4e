import re
import spacy
import os
import logging
import requests
from typing import Set

# Set up logging
logger = logging.getLogger(__name__)

# Microservice URL from environment variable
SCISPA_MICROSERVICE_URL = os.getenv("SCI_NAME_MICROSERVICE_URL")

# Load ScispaCy CRAFT model only once (for local fallback)
try:
    _scispacy_nlp = spacy.load("en_ner_craft_md")
except Exception as e:
    _scispacy_nlp = None
    logger.warning(f"ScispaCy CRAFT model not loaded: {e} (scientific name detection will use regex fallback only)")

def _find_scispacy_names(text: str) -> Set[str]:
    """
    Use ScispaCy CRAFT model to extract scientific names (TAXON entities) from text.
    Returns a set of unique names.
    """
    if not _scispacy_nlp:
        return set()
    doc = _scispacy_nlp(text)
    return set(ent.text for ent in doc.ents if ent.label_ == "TAXON")

def _find_regex_names(text: str) -> Set[str]:
    """
    Use expanded regex to extract:
    - 'Genus sp.' or 'Genus spp.' optionally followed by a number (e.g., 'Genus sp. 3')
    - Single-word genus names in a list context (e.g., '5. Amphiroa')
    Returns a set of unique names.
    """
    names = set()
    # Improved regex for 'Genus sp.' or 'Genus spp.' optionally followed by a number
    pattern = r"\b([A-Z][a-z]+\s(?:sp\.|spp\.)(?:\s*\d+)?)\b"
    for match in re.finditer(pattern, text):
        names.add(match.group(1))
    # Improved regex for binomial names (Genus species)
    binomial_pattern = r"\b([A-Z][a-z]+\s[a-z]+)\b"
    for match in re.finditer(binomial_pattern, text):
        names.add(match.group(1))
    # Match single-word genus names in numbered/bulleted lists
    list_pattern = r"^(?:\s*[-*\d]+[.)]?\s+)([A-Z][a-z]+)\s*$"
    for line in text.splitlines():
        m = re.match(list_pattern, line)
        if m:
            names.add(m.group(1))
    return names

def validate_and_italicize_scientific_names(text: str) -> str:
    """
    Italicize all scientific names in the text using the microservice if available, otherwise
    use ScispaCy CRAFT model (TAXON entities) as primary, and expanded regex as fallback.
    Avoids double-wrapping and reduces false positives.
    """
    if not text:
        return text
    # 1. Try microservice if configured
    if SCISPA_MICROSERVICE_URL:
        try:
            logger.info(f"Calling microservice at {SCISPA_MICROSERVICE_URL} for text: {text[:100]}")
            resp = requests.post(
                SCISPA_MICROSERVICE_URL,
                json={"text": text},
                timeout=5
            )
            logger.info(f"Microservice response: {resp.text}")
            if resp.status_code == 200:
                result = resp.json().get("result", text)
                logger.info(f"Microservice returned result: {result[:100]}")
                return result
            else:
                logger.warning(f"Microservice returned status {resp.status_code}: {resp.text}")
        except Exception as e:
            logger.warning(f"Could not reach scientific name microservice: {e}")
    # 2. Local fallback
    logger.info("Using local fallback for scientific name italicization.")
    scispacy_names = _find_scispacy_names(text)
    regex_names = _find_regex_names(text)
    all_names = scispacy_names | regex_names
    sorted_names = sorted(all_names, key=len, reverse=True)
    result = text
    for name in sorted_names:
        result = re.sub(rf"(?<!<em>)\\b{re.escape(name)}\\b(?!</em>)", f"<em>{name}</em>", result)
    logger.info(f"Result after fallback italicization: {result[:200]}")
    return result

# Example usage:
if __name__ == "__main__":
    sample = "1. Dictyota sp. 3\n2. Fucus sp. 1\n3. Sargassum sp.\n4. Padina spp.\n5. Amphiroa\nHow does the presence of Sargassum sp. affect water quality?"
    print(validate_and_italicize_scientific_names(sample)) 