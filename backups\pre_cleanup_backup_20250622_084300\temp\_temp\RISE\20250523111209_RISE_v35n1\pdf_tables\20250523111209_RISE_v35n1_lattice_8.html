<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;" border="1" class="dataframe table table-sm table-bordered table-responsive">
  <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"ead>
    <tr style="text-align: right;">
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"></th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Materials \nMaterials</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Amount (A) \nAmount (A)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Quantity (per \nplot) (B) \nplot) (B)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Quantity (per  | Cost per plot \nCost per plot \n(AxB) \n( AxB)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">No of plots \nNo of plots \napplicable \napplicable</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Corn Seed \nCorn Seed</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">500/kg \n500/kg</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.5 kg \n0.5 kg</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">250 \n250</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">20 \n20</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Malungay \nMalungay</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">5/pc \n5/pc</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">72 pcs \n72 pcs</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">360 \n360</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">5 \n5</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Lemongrass \nLemongrass</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">1/pc \n1/pc</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">144 pcs \n144 pcs</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">144 \n144</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">5 \n5</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Fertilizer \nFertilizer\n(46-0-0) \n (46-0-0)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">19/kg \n19/kg</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">10 kgs \n10 kgs</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">10 kgs \n10 kgs</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">20 \n20</td>
    </tr>
  </tbody>
</table>