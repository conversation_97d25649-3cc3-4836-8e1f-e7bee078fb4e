"""
Updated Flask Configuration using Centralized Configuration Manager

This uses the centralized ConfigManager for all settings while maintaining
backward compatibility with the existing Flask configuration interface.
"""

import os
from datetime import timedelta
from config.config_manager import get_config

class Config:
    """Base configuration class using centralized config manager"""

    def __init__(self):
        self.config_manager = get_config()
        self._setup_flask_config()

    def _setup_flask_config(self):
        """Setup Flask-specific configuration from centralized config"""
        security_config = self.config_manager.get_security_config()
        file_config = self.config_manager.get_file_upload_config()
        db_config = self.config_manager.get_database_config()

        # Flask settings
        self.SECRET_KEY = security_config.secret_key
        self.FLASK_SECRET_KEY = security_config.secret_key

        # Database settings
        self.SQLALCHEMY_DATABASE_URI = f"sqlite:///{db_config.user_db_path}"
        self.SQLALCHEMY_TRACK_MODIFICATIONS = False

        # File upload settings
        self.MAX_CONTENT_LENGTH = file_config.max_content_length
        self.UPLOAD_FOLDER = file_config.upload_folder

        # Security settings
        self.WTF_CSRF_ENABLED = security_config.csrf_enabled
        self.WTF_CSRF_TIME_LIMIT = security_config.csrf_time_limit

        # Session settings
        self.PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
        self.SESSION_COOKIE_SECURE = False  # Will be overridden in production
        self.SESSION_COOKIE_HTTPONLY = True
        self.SESSION_COOKIE_SAMESITE = 'Lax'

        # Rate limiting
        rate_config = self.config_manager.get("rate_limiting", {})
        self.RATELIMIT_DEFAULT = rate_config.get("default_limits", "200 per day;50 per hour;10 per minute")
        self.RATELIMIT_STORAGE_URL = rate_config.get("storage_url", "memory://")

        # AI Model settings (backward compatibility)
        self.OLLAMA_BASE_URL = self.config_manager.get("ollama.base_url", "http://localhost:11434")
        self.DEFAULT_LLM_MODEL = self.config_manager.get("llm_model", "llama3.1:8b-instruct-q4_K_M")
        self.DEFAULT_EMBEDDING_MODEL = self.config_manager.get("embedding_model", "mxbai-embed-large:latest")
        self.DEFAULT_VISION_MODEL = self.config_manager.get("vision_model", "llama3.2-vision:11b-instruct-q4_K_M")

        # Feature toggles
        self.USE_VISION_MODEL = self.config_manager.get("use_vision_model", True)
        self.USE_VISION_MODEL_DURING_EMBEDDING = self.config_manager.get("use_vision_model_during_embedding", True)
        self.FILTER_PDF_IMAGES = self.config_manager.get("filter_pdf_images", True)

        # Logging
        logging_config = self.config_manager.get("logging", {})
        self.LOG_LEVEL = logging_config.get("level", "INFO")

    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        pass

class DevelopmentConfig(Config):
    """Development configuration"""

    def __init__(self):
        super().__init__()
        self.DEBUG = True
        self.TESTING = False

        # Development-specific overrides
        features = self.config_manager.get("features", {})
        self.DEBUG = features.get("debug_mode", True)
        self.TEMPLATES_AUTO_RELOAD = features.get("auto_reload", True)

        # Development database settings
        self.SQLALCHEMY_ECHO = features.get("debug_mode", True)
        self.WTF_CSRF_ENABLED = False  # Disable CSRF for development API testing

class TestingConfig(Config):
    """Testing configuration"""

    def __init__(self):
        super().__init__()
        self.TESTING = True
        self.DEBUG = True

        # Use in-memory database for testing
        self.SQLALCHEMY_DATABASE_URI = "sqlite:///:memory:"

        # Disable CSRF for testing
        self.WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """Production configuration"""

    def __init__(self):
        super().__init__()
        self.DEBUG = False
        self.TESTING = False

        # Production-specific settings
        features = self.config_manager.get("features", {})
        self.DEBUG = features.get("debug_mode", False)

        # Production security settings
        self.SESSION_COOKIE_SECURE = True
        self.SESSION_COOKIE_HTTPONLY = True
        self.SESSION_COOKIE_SAMESITE = 'Strict'

        # Production logging
        logging_config = self.config_manager.get("logging", {})
        self.LOG_LEVEL = logging_config.get("level", "WARNING")

        # Enhanced security for production
        security_config = self.config_manager.get_security_config()
        if security_config.secret_key == "dev-secret-key-change-in-production":
            raise ValueError("Production secret key must be set via environment variable")

    @classmethod
    def init_app(cls, app):
        """Initialize production-specific settings"""
        Config.init_app(app)

        # Production logging configuration
        import logging
        from logging.handlers import RotatingFileHandler

        if not app.debug and not app.testing:
            if not os.path.exists('logs'):
                os.mkdir('logs')

            file_handler = RotatingFileHandler(
                'logs/erdb_app.log',
                maxBytes=10240000,
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)

            app.logger.setLevel(logging.INFO)
            app.logger.info('ERDB Document Management System startup')

class StagingConfig(Config):
    """Staging configuration"""

    def __init__(self):
        super().__init__()
        self.DEBUG = True
        self.TESTING = False

        # Staging-specific settings
        self.SQLALCHEMY_ECHO = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'staging': StagingConfig,
    'default': DevelopmentConfig
}