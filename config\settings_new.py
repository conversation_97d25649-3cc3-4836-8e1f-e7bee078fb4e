"""
Updated Flask Configuration using Centralized Configuration Manager

This replaces the old config/settings.py with a version that uses
the centralized ConfigManager for all settings.
"""

import os
from config.config_manager import get_config

class Config:
    """Base configuration class using centralized config manager"""
    
    def __init__(self):
        self.config_manager = get_config()
        self._setup_flask_config()
    
    def _setup_flask_config(self):
        """Setup Flask-specific configuration from centralized config"""
        security_config = self.config_manager.get_security_config()
        file_config = self.config_manager.get_file_upload_config()
        db_config = self.config_manager.get_database_config()
        
        # Flask settings
        self.SECRET_KEY = security_config.secret_key
        self.FLASK_SECRET_KEY = security_config.secret_key
        
        # Database settings
        self.SQLALCHEMY_DATABASE_URI = f"sqlite:///{db_config.user_db_path}"
        self.SQLALCHEMY_TRACK_MODIFICATIONS = False
        
        # File upload settings
        self.MAX_CONTENT_LENGTH = file_config.max_content_length
        self.UPLOAD_FOLDER = file_config.upload_folder
        
        # Security settings
        self.WTF_CSRF_ENABLED = security_config.csrf_enabled
        self.WTF_CSRF_TIME_LIMIT = security_config.csrf_time_limit
        
        # Rate limiting
        rate_config = self.config_manager.get("rate_limiting", {})
        self.RATELIMIT_DEFAULT = rate_config.get("default_limits", "200 per day;50 per hour;10 per minute")
        self.RATELIMIT_STORAGE_URL = rate_config.get("storage_url", "memory://")

class DevelopmentConfig(Config):
    """Development configuration"""
    
    def __init__(self):
        super().__init__()
        self.DEBUG = True
        self.TESTING = False
        
        # Development-specific overrides
        features = self.config_manager.get("features", {})
        self.DEBUG = features.get("debug_mode", True)
        self.TEMPLATES_AUTO_RELOAD = features.get("auto_reload", True)

class TestingConfig(Config):
    """Testing configuration"""
    
    def __init__(self):
        super().__init__()
        self.TESTING = True
        self.DEBUG = True
        
        # Use in-memory database for testing
        self.SQLALCHEMY_DATABASE_URI = "sqlite:///:memory:"
        
        # Disable CSRF for testing
        self.WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """Production configuration"""
    
    def __init__(self):
        super().__init__()
        self.DEBUG = False
        self.TESTING = False
        
        # Production-specific settings
        features = self.config_manager.get("features", {})
        self.DEBUG = features.get("debug_mode", False)
        
        # Enhanced security for production
        security_config = self.config_manager.get_security_config()
        if security_config.secret_key == "dev-secret-key-change-in-production":
            raise ValueError("Production secret key must be set via environment variable")

# Configuration mapping
config = {
    'development': DevelopmentConfig(),
    'testing': TestingConfig(),
    'production': ProductionConfig(),
    'default': DevelopmentConfig()
}

def get_flask_config(environment=None):
    """Get Flask configuration for specified environment"""
    if environment is None:
        environment = os.environ.get('FLASK_ENV', 'development')
    
    return config.get(environment.lower(), config['default'])
