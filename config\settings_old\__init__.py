"""
Settings package for configuration management.
"""

import os

class DefaultConfig:
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-default-secret-key')
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', 'sqlite:///chat_history.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    # Add other default Flask config values as needed

config = {
    'default': DefaultConfig,
    'development': DefaultConfig,
    'production': DefaultConfig,
    'testing': DefaultConfig,
} 